pub mod web;

pub trait App: approck::server::App + approck_postgres::App + auth_fence::App {}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool {
        self.is_logged_in()
    }
    fn api_usage(&self) -> bool {
        self.is_logged_in()
    }
    fn guard_add(&self) -> bool {
        true
    }
    fn guard_read(&self, _guard_uuid: granite::Uuid) -> bool {
        true
    }
    fn guard_list(&self) -> bool {
        true
    }
    fn schedule_add(&self) -> bool {
        true
    }
    fn schedule_read(&self, _guard_schedule_uuid: granite::Uuid) -> bool {
        true
    }
    fn schedule_write(&self, _guard_schedule_uuid: granite::Uuid) -> bool {
        true
    }
    fn schedule_list(&self) -> bool {
        true
    }
    fn attendance_add(&self) -> bool {
        true
    }
    fn attendance_read(&self, _guard_uuid: granite::Uuid) -> bool {
        true
    }
    fn attendance_list(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base + bux::document::PageNav + auth_fence::Document {}

pub fn ml_guard(guard_uuid: granite::Uuid) -> String {
    format!("/malawi/guard/{guard_uuid}/")
}

pub fn ml_guard_list() -> String {
    "/malawi/guard/".to_string()
}

pub fn ml_guard_add() -> String {
    "/malawi/guard/add".to_string()
}

pub fn ml_guard_edit(guard_uuid: granite::Uuid) -> String {
    format!("/malawi/guard/{guard_uuid}/edit")
}

pub fn ml_guard_delete(guard_uuid: granite::Uuid) -> String {
    format!("/malawi/guard/{guard_uuid}/delete")
}

pub fn ml_guard_schedule(guard_schedule_uuid: granite::Uuid) -> String {
    format!("/malawi/schedule/{guard_schedule_uuid}/")
}

pub fn ml_guard_attendance(guard_attendance_uuid: granite::Uuid) -> String {
    format!("/malawi/attendance/{guard_attendance_uuid}/")
}

pub fn ml_attendance_list() -> String {
    "/malawi/attendance/".to_string()
}

pub fn ml_attendance_add() -> String {
    "/malawi/attendance/add".to_string()
}

pub fn ml_schedule_list() -> String {
    "/malawi/schedule/".to_string()
}

pub fn ml_schedule_add() -> String {
    "/malawi/schedule/add".to_string()
}

pub fn ml_schedule_edit(schedule_uuid: granite::Uuid) -> String {
    format!("/malawi/schedule/{schedule_uuid}/edit")
}

pub fn ml_schedule_delete(guard_schedule_uuid: granite::Uuid) -> String {
    format!("/malawi/schedule/{guard_schedule_uuid}/delete")
}
